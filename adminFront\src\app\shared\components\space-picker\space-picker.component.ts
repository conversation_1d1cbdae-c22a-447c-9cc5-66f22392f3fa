import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NbFormFieldModule, NbInputModule, NbButtonModule, NbIconModule, NbCardModule } from '@nebular/theme';
import { PaginationComponent } from 'src/app/pages/components/pagination/pagination.component';
import { SpaceService } from 'src/services/api/services/space.service';
import { GetSpaceListResponse } from 'src/services/api/models';
import { tap } from 'rxjs/operators';

export interface SpacePickerItem {
  CSpaceID: number;
  CPart: string;
  CLocation?: string | null;
  selected?: boolean;
}

@Component({
  selector: 'app-space-picker',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbFormFieldModule,
    NbInputModule,
    NbButtonModule,
    NbIconModule,
    NbCardModule,
    PaginationComponent
  ],
  templateUrl: './space-picker.component.html',
  styleUrls: ['./space-picker.component.scss']
})
export class SpacePickerComponent implements OnInit {
  @Input() selectedItems: SpacePickerItem[] = [];
  @Input() multiple: boolean = true;
  @Input() placeholder: string = '請選擇空間';
  @Output() selectionChange = new EventEmitter<SpacePickerItem[]>();

  // 搜尋相關屬性
  searchKeyword: string = '';
  searchLocation: string = '';
  isLoading: boolean = false;

  // 分頁相關屬性
  pageIndex = 1;
  pageSize = 10;
  totalRecords = 0;

  // 資料相關屬性
  availableSpaces: SpacePickerItem[] = [];
  allSelected = false;

  constructor(private spaceService: SpaceService) { }

  ngOnInit(): void {
    this.loadAvailableSpaces();
  }

  // 載入可用空間列表
  loadAvailableSpaces(): void {
    this.isLoading = true;
    const request = {
      CPart: this.searchKeyword || null,
      CLocation: this.searchLocation || null,
      CStatus: 1, // 只顯示啟用的空間
      PageIndex: this.pageIndex,
      PageSize: this.pageSize
    };

    this.spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.availableSpaces = response.Entries?.map(item => ({
            CSpaceID: item.CSpaceID!,
            CPart: item.CPart!,
            CLocation: item.CLocation,
            selected: this.selectedItems.some(s => s.CSpaceID === item.CSpaceID)
          })) || [];
          this.totalRecords = response.TotalItems || 0;
          this.updateAllSelectedState();
        }
        this.isLoading = false;
      })
    ).subscribe({
      next: () => {},
      error: () => {
        this.isLoading = false;
      }
    });
  }

  // 搜尋功能
  onSearch(): void {
    this.pageIndex = 1;
    this.loadAvailableSpaces();
  }

  // 重置搜尋
  onReset(): void {
    this.searchKeyword = '';
    this.searchLocation = '';
    this.pageIndex = 1;
    this.loadAvailableSpaces();
  }

  // 切換空間選擇
  toggleSpaceSelection(space: SpacePickerItem): void {
    space.selected = !space.selected;

    if (space.selected) {
      if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {
        if (this.multiple) {
          this.selectedItems.push({ ...space });
        } else {
          this.selectedItems = [{ ...space }];
          // 單選模式下，取消其他選項
          this.availableSpaces.forEach(s => {
            if (s.CSpaceID !== space.CSpaceID) {
              s.selected = false;
            }
          });
        }
      }
    } else {
      this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);
    }

    this.updateAllSelectedState();
    this.selectionChange.emit([...this.selectedItems]);
  }

  // 全選/取消全選
  toggleAllSpaces(): void {
    this.allSelected = !this.allSelected;

    this.availableSpaces.forEach(space => {
      if (this.allSelected && !space.selected) {
        space.selected = true;
        if (!this.selectedItems.some(s => s.CSpaceID === space.CSpaceID)) {
          this.selectedItems.push({ ...space });
        }
      } else if (!this.allSelected && space.selected) {
        space.selected = false;
        this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);
      }
    });

    this.selectionChange.emit([...this.selectedItems]);
  }

  // 移除已選空間
  removeSelectedSpace(space: SpacePickerItem): void {
    this.selectedItems = this.selectedItems.filter(s => s.CSpaceID !== space.CSpaceID);

    // 更新可用列表中的選中狀態
    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);
    if (availableSpace) {
      availableSpace.selected = false;
    }

    this.updateAllSelectedState();
    this.selectionChange.emit([...this.selectedItems]);
  }

  // 更新全選狀態
  updateAllSelectedState(): void {
    this.allSelected = this.availableSpaces.length > 0 &&
      this.availableSpaces.every(space => space.selected);
  }

  // 分頁變更 - 與 ngx-pagination 組件兼容
  onPageChange(page: number): void {
    this.pageIndex = page;
    this.loadAvailableSpaces();
  }

  // 計算總頁數
  get totalPages(): number {
    return Math.ceil(this.totalRecords / this.pageSize);
  }
}
