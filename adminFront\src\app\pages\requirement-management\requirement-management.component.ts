import { Component, DestroyRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { EnumHelper } from 'src/app/shared/helper/enumHelper';
import { PetternHelper } from 'src/app/shared/helper/petternHelper';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, RequirementService } from 'src/services/api/services';
import { BaseComponent } from '../components/base/baseComponent';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';
import { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';
import { FormsModule } from '@angular/forms';
import { NgFor, NgIf } from '@angular/common';
import { PaginationComponent } from '../components/pagination/pagination.component';
import { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';
import { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';
import { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';
import { EnumHouseType } from 'src/app/shared/enum/enumHouseType';
import { SpaceTemplateSelectorService } from 'src/app/shared/components/space-template-selector/space-template-selector.service';
import { SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';
import { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';
// 批次編輯配置介面
export interface BatchEditConfig {
  title: string;
  noticeText: string;
  noticeItems: string[];
  confirmButtonText: string;
  cancelButtonText: string;
}

@Component({
  selector: 'app-requirement-management',
  standalone: true,
  imports: [
    NbCardModule,
    BreadcrumbComponent,
    NbInputModule,
    FormsModule,
    NbSelectModule,
    NbOptionModule,
    NgIf,
    NgFor,
    PaginationComponent,
    StatusPipe,
    NbCheckboxModule,
    FormGroupComponent,
    NumberWithCommasPipe,
    SpaceTemplateSelectorButtonComponent,
  ],
  templateUrl: './requirement-management.component.html',
  styleUrl: './requirement-management.component.scss'
})
export class RequirementManagementComponent extends BaseComponent implements OnInit {
  @ViewChild('batchEditDialog', { static: false }) batchEditDialog!: TemplateRef<any>;

  constructor(
    private _allow: AllowHelper,
    private enumHelper: EnumHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private buildCaseService: BuildCaseService,
    private requirementService: RequirementService,
    private pettern: PetternHelper,
    private router: Router,
    private destroyref: DestroyRef,
    private spaceTemplateSelectorService: SpaceTemplateSelectorService
  ) {
    super(_allow);
    this.initializeSearchForm();
    this.getBuildCaseList();
  }

  // request
  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null };
  getRequirementRequest: GetRequirementByIdRequest = {};

  // response
  buildCaseList: BuildCaseGetListReponse[] = [];
  requirementList: GetRequirement[] = [];
  saveRequirement: SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean } = { CHouseType: [] };

  statusOptions = [
    { value: 0, label: '停用' },
    { value: 1, label: '啟用' },
  ];
  houseType = this.enumHelper.getEnumOptions(EnumHouseType);
  isNew = false;
  currentBuildCase = 0;

  // 批次編輯相關屬性
  selectedItems: GetRequirement[] = [];
  isAllSelected = false;
  isBatchEditMode = false;
  // 批次編輯時的項目資料副本
  batchEditItems: (SaveDataRequirement & { CIsShow?: boolean; CIsSimple?: boolean })[] = [];
  // 批次編輯配置
  batchEditConfig: BatchEditConfig = this.getDefaultBatchEditConfig();

  override ngOnInit(): void { }

  // 初始化搜尋表單
  initializeSearchForm() {
    this.getListRequirementRequest.CStatus = -1;
    this.getListRequirementRequest.CIsShow = null;
    this.getListRequirementRequest.CIsSimple = null;
    this.getListRequirementRequest.CRequirement = '';
    this.getListRequirementRequest.CLocation = '';
    // 預設全選所有房屋類型
    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);
  }

  // 取得預設批次編輯配置
  getDefaultBatchEditConfig(): BatchEditConfig {
    return {
      title: '批次編輯建案需求',
      noticeText: '您可以個別修改每個項目的欄位',
      noticeItems: [
        '工程項目、類型、排序、狀態、單價、單位為必填欄位',
        '排序和單價不能為負數',
        '區域最多20個字，工程項目最多50個字，備註說明最多100個字'
      ],
      confirmButtonText: '確定批次更新',
      cancelButtonText: '取消'
    };
  }

  // 取得模板新增批次編輯配置
  getTemplateAddBatchEditConfig(): BatchEditConfig {
    return {
      title: '模板新增批次編輯',
      noticeText: '從模板載入的項目，您可以個別修改每個項目的欄位',
      noticeItems: [
        '工程項目、類型、排序、狀態、單價、單位為必填欄位',
        '排序和單價不能為負數',
        '區域最多20個字，工程項目最多50個字，備註說明最多100個字',
        '模板項目已自動填入預設值，請檢查並調整各項目設定'
      ],
      confirmButtonText: '確定新增項目',
      cancelButtonText: '取消'
    };
  }

  // 建案切換事件處理
  onBuildCaseChange(newBuildCaseId: any) {
    // 如果在批次編輯模式下切換建案，給予警告
    if (this.isBatchEditMode || this.selectedItems.length > 0) {
      const confirmMessage = this.isBatchEditMode
        ? '切換建案將會關閉批次編輯對話框並清除所有選擇的項目，是否繼續？'
        : `切換建案將會清除已選擇的 ${this.selectedItems.length} 個項目，是否繼續？`;

      if (!confirm(confirmMessage)) {
        // 使用者取消，恢復原來的建案選擇
        setTimeout(() => {
          this.getListRequirementRequest.CBuildCaseID = this.currentBuildCase;
        }, 0);
        return;
      }
    }

    // 重置批次選擇的項目
    this.selectedItems = [];
    this.isAllSelected = false;
    this.batchEditItems = [];
    this.isBatchEditMode = false;

    // 更新當前建案並重新載入資料
    this.currentBuildCase = newBuildCaseId;
    this.getList();
  }

  // 重置搜尋
  resetSearch() {
    this.initializeSearchForm();
    // 清除選擇狀態和批次編輯相關狀態
    this.selectedItems = [];
    this.isAllSelected = false;
    this.batchEditItems = [];
    this.isBatchEditMode = false;

    // 重置後如果有建案資料，重新設定預設選擇第一個建案
    if (this.buildCaseList && this.buildCaseList.length > 0) {
      setTimeout(() => {
        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;
        this.getList();
      }, 0);
    } else {
      this.getList();
    }
  }

  getHouseType(hTypes: number[] | number | null | undefined): string {
    if (!hTypes) {
      return '';
    }

    if (!Array.isArray(hTypes)) {
      hTypes = [hTypes];
    }

    let labels: string[] = [];
    hTypes.forEach(htype => {
      let findH = this.houseType.find(x => x.value == htype);
      if (findH) {
        labels.push(findH.label);
      }
    });
    return labels.join(', ');
  }

  validation() {
    this.valid.clear();

    // 建案頁面需要驗證建案名稱
    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);
    this.valid.required('[需求]', this.saveRequirement.CRequirement);
    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);
    this.valid.required('[排序]', this.saveRequirement.CSort);
    this.valid.required('[狀態]', this.saveRequirement.CStatus);
    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);
    this.valid.required('[單位]', this.saveRequirement.CUnit);

    // 數值範圍驗證
    if (this.saveRequirement.CSort !== null && this.saveRequirement.CSort !== undefined && this.saveRequirement.CSort < 0) {
      this.valid.errorMessages.push('[排序] 不能為負數');
    }

    if (this.saveRequirement.CUnitPrice !== null && this.saveRequirement.CUnitPrice !== undefined && this.saveRequirement.CUnitPrice < 0) {
      this.valid.errorMessages.push('[單價] 不能為負數');
    }

    // 長度驗證
    if (this.saveRequirement.CLocation && this.saveRequirement.CLocation.length > 20) {
      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');
    }

    if (this.saveRequirement.CRequirement && this.saveRequirement.CRequirement.length > 50) {
      this.valid.errorMessages.push('[工程項目] 不能超過50個字');
    }

    // 備註說明長度驗證
    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {
      this.valid.errorMessages.push('[備註說明] 不能超過100個字');
    }
  }

  add(dialog: TemplateRef<any>) {
    this.isNew = true;
    this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };
    this.saveRequirement.CStatus = 1;
    this.saveRequirement.CUnitPrice = 0;

    // 建案頁面 - 使用當前選擇的建案或第一個建案
    if (this.currentBuildCase != 0) {
      this.saveRequirement.CBuildCaseID = this.currentBuildCase;
    } else if (this.buildCaseList && this.buildCaseList.length > 0) {
      this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;
    }

    this.dialogService.open(dialog);
  }

  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {
    this.getRequirementRequest.CRequirementID = data.CRequirementID!;
    this.isNew = false;
    try {
      await this.getData();
      this.dialogService.open(dialog);
    } catch (error) {
      console.log("Failed to get function data", error)
    }
  }

  save(ref: any) {
    this.validation();
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return;
    }

    this.requirementService.apiRequirementSaveDataPost$Json({
      body: this.saveRequirement
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.message.showSucessMSG('執行成功');
        this.getList();
      } else {
        this.message.showErrorMSG(res.Message!)
      }
    });
    ref.close();
  }

  onDelete(data: GetRequirement) {
    this.saveRequirement.CRequirementID = data.CRequirementID!;
    this.isNew = false;
    if (window.confirm('是否確定刪除?')) {
      this.remove();
    } else {
      return;
    }
  }

  remove() {
    this.requirementService.apiRequirementDeleteDataPost$Json({
      body: {
        CRequirementID: this.saveRequirement.CRequirementID!
      }
    }).subscribe(res => {
      this.message.showSucessMSG('執行成功');
      this.getList();
    });
  }

  getBuildCaseList() {
    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })
      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {
        this.buildCaseList = res.Entries!;
        // 如果有建案時才查詢
        if (this.buildCaseList.length > 0) {
          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;
          this.getList();
        }
      })
  }

  getList() {
    this.getListRequirementRequest.PageSize = this.pageSize;
    this.getListRequirementRequest.PageIndex = this.pageIndex;
    this.requirementList = [] as GetRequirement[];
    this.totalRecords = 0;

    // 建案頁面的邏輯
    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {
      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;
    }

    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })
      .pipe()
      .subscribe(res => {
        if (res.StatusCode == 0) {
          if (res.Entries) {
            this.requirementList = res.Entries;
            this.totalRecords = res.TotalItems!;

            // 清理已選擇的項目，移除不存在於新列表中的項目
            const originalSelectedCount = this.selectedItems.length;
            this.selectedItems = this.selectedItems.filter(selectedItem =>
              this.requirementList.some(listItem => listItem.CRequirementID === selectedItem.CRequirementID)
            );

            // 如果選擇的項目數量有變化，清理批次編輯狀態
            if (originalSelectedCount !== this.selectedItems.length || this.selectedItems.length === 0) {
              this.batchEditItems = [];
              this.isBatchEditMode = false;
            }

            // 更新選擇狀態
            this.updateSelectAllState();
          }
        }
      })
  }

  getData() {
    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })
      .pipe()
      .subscribe(res => {
        if (res.StatusCode == 0) {
          if (res.Entries) {
            this.saveRequirement = { CHouseType: [], CIsShow: false, CIsSimple: false };
            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;
            this.saveRequirement.CLocation = res.Entries.CLocation;
            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];
            this.saveRequirement.CRemark = res.Entries.CRemark;
            this.saveRequirement.CRequirement = res.Entries.CRequirement;
            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;
            this.saveRequirement.CSort = res.Entries.CSort;
            this.saveRequirement.CStatus = res.Entries.CStatus;
            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice ?? 0;
            this.saveRequirement.CUnit = res.Entries.CUnit;
            this.saveRequirement.CSpaceId = res.Entries.CSpaceId || null;
            this.saveRequirement.CIsShow = res.Entries.CIsShow || false;
            this.saveRequirement.CIsSimple = res.Entries.CIsSimple || false;
          }
        }
      })
  }

  onHouseTypeChange(value: number, checked: any) {
    console.log(checked);

    if (checked) {
      if (!this.saveRequirement.CHouseType?.includes(value)) {
        this.saveRequirement.CHouseType?.push(value);
      }
      console.log(this.saveRequirement.CHouseType);
    } else {
      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);
    }
  }

  getCIsShowText(data: any): string {
    return data.CIsShow ? '是' : '否';
  }

  getCIsSimpleText(data: any): string {
    return data.CIsSimple ? '是' : '否';
  }

  // 空間模板相關方法
  onSpaceTemplateApplied(config: SpaceTemplateConfig) {
    console.log('套用空間模板配置:', config);

    // 直接將模板項目轉換為批次編輯項目，使用從模板選擇器傳來的明細數據
    this.convertTemplatesToBatchEdit(config.selectedItems, config.templateDetails);
  }

  onTemplateError(errorMessage: string) {
    this.message.showErrorMSG(errorMessage);
  }

  // 需求選擇相關方法
  onRequirementSelectionConfirmed(config: RequirementSelectionConfig) {
    console.log('確認需求選擇配置:', config);

    // 將選擇的需求項目加入到批次編輯
    this.convertRequirementsToSelection(config.selectedItems);

    this.message.showSucessMSG(`已選擇 ${config.selectedItems.length} 個需求項目`);
  }

  onRequirementSelectionCancelled() {
    console.log('取消需求選擇');
  }

  onRequirementSelectionError(errorMessage: string) {
    this.message.showErrorMSG(errorMessage);
  }

  // 將選擇的需求項目轉換為選擇狀態
  private convertRequirementsToSelection(selectedRequirements: any[]) {
    if (!selectedRequirements || selectedRequirements.length === 0) {
      return;
    }

    // 清除當前選擇
    this.selectedItems = [];
    this.isAllSelected = false;

    // 將選擇的需求項目加入到當前選擇列表
    selectedRequirements.forEach(requirement => {
      const existingItem = this.requirementList.find(item =>
        item.CRequirementID === requirement.CRequirementID
      );

      if (existingItem) {
        this.selectedItems.push(existingItem);
      }
    });

    // 更新全選狀態
    this.updateSelectAllState();
  }

  // 將模板項目轉換為批次編輯項目
  private convertTemplatesToBatchEdit(selectedTemplates: any[], templateDetails: Map<number, any[]>) {
    if (!this.getListRequirementRequest.CBuildCaseID) {
      this.message.showErrorMSG('建案 ID 不存在');
      return;
    }

    // 清除當前選擇的項目
    this.selectedItems = [];
    this.isAllSelected = false;

    // 取得預設排序值 - 從列表最大排序值開始
    const maxSort = this.requirementList.length > 0
      ? Math.max(...this.requirementList.map(item => item.CSort || 0))
      : 0;

    let currentSortIndex = 0;
    const allBatchEditItems: any[] = [];

    // 處理每個模板的明細項目
    selectedTemplates.forEach(template => {
      const details = templateDetails.get(template.CTemplateId) || [];

      if (details && details.length > 0) {
        // 將每個明細項目轉換為批次編輯項目
        details.forEach((detail, detailIndex) => {
          const batchEditItem = {
            CRequirementID: undefined, // 新項目沒有 ID
            CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,
            CLocation: detail.CLocation || template.CLocation || '', // 優先使用明細的位置，其次使用模板位置
            CRequirement: detail.CPart || `${template.CTemplateName} - 項目 ${detailIndex + 1}`, // 使用明細名稱作為工程項目
            CHouseType: template.CHouseType || [], // 從模板獲取房屋類型，預設為空陣列
            CSort: maxSort + currentSortIndex + 1, // 從最大排序值往上遞增
            CStatus: 1, // 預設啟用
            CUnitPrice: (detail as any).CUnitPrice || (template as any).CUnitPrice || 0, // 優先使用明細單價，其次使用模板單價
            CUnit: (detail as any).CUnit || (template as any).CUnit || '式', // 優先使用明細單位，其次使用模板單位
            CSpaceId: detail.CReleateId || (template as any).cRelateID || null, // 從明細獲取關聯空間 ID
            CIsShow: template.CIsShow !== undefined ? template.CIsShow : false, // 預設不顯示在預約需求
            CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false, // 預設不是簡易客變
            CRemark: (detail as any).CRemark || `從模板「${template.CTemplateName}」的明細項目「${detail.CPart}」產生` // 記錄來源模板和明細
          };

          allBatchEditItems.push(batchEditItem);
          currentSortIndex++;
        });
      } else {
        // 如果模板沒有明細，則將模板本身作為一個項目
        const batchEditItem = {
          CRequirementID: undefined, // 新項目沒有 ID
          CBuildCaseID: this.getListRequirementRequest.CBuildCaseID,
          CLocation: template.CLocation || '', // 從模板獲取區域資訊
          CRequirement: template.CTemplateName || `模板項目 ${currentSortIndex + 1}`, // 使用模板名稱作為工程項目
          CHouseType: template.CHouseType || [], // 從模板獲取房屋類型，預設為空陣列
          CSort: maxSort + currentSortIndex + 1, // 從最大排序值往上遞增
          CStatus: 1, // 預設啟用
          CUnitPrice: (template as any).CUnitPrice || 0, // 從模板獲取單價，預設為 0
          CUnit: (template as any).CUnit || '式', // 從模板獲取單位，預設為 '式'
          CSpaceId: (template as any).cRelateID || null, // 從模板獲取關聯空間 ID
          CIsShow: template.CIsShow !== undefined ? template.CIsShow : false, // 預設不顯示在預約需求
          CIsSimple: template.CIsSimple !== undefined ? template.CIsSimple : false, // 預設不是簡易客變
          CRemark: (template as any).CRemark || `從模板「${template.CTemplateName}」產生` // 記錄來源模板
        };

        allBatchEditItems.push(batchEditItem);
        currentSortIndex++;
      }
    });

    // 設定批次編輯項目
    this.batchEditItems = allBatchEditItems;

    // 設定為模板新增批次編輯模式，使用專用配置
    this.batchEditConfig = this.getTemplateAddBatchEditConfig();
    this.isBatchEditMode = true;

    // 計算總明細數量
    const totalDetailCount = selectedTemplates.reduce((sum, template) => {
      const details = templateDetails.get(template.CTemplateId) || [];
      return sum + (details.length || 1);
    }, 0);

    // 直接開啟批次編輯對話框
    setTimeout(() => {
      this.dialogService.open(this.batchEditDialog);
    }, 100);
  }  // 批次編輯相關方法

  // 切換單一項目選擇狀態
  toggleItemSelection(item: GetRequirement) {
    const index = this.selectedItems.findIndex(selected => selected.CRequirementID === item.CRequirementID);
    if (index > -1) {
      this.selectedItems.splice(index, 1);
    } else {
      this.selectedItems.push(item);
    }
    this.updateSelectAllState();
  }

  // 切換全選狀態
  toggleSelectAll(newValue: boolean) {
    if (this.requirementList.length === 0) {
      this.selectedItems = [];
      this.isAllSelected = false;
      return;
    }

    // 更新 isAllSelected 狀態
    this.isAllSelected = newValue;

    // 根據新值更新 selectedItems
    if (this.isAllSelected) {
      this.selectedItems = [...this.requirementList];
    } else {
      this.selectedItems = [];
    }
  }

  // 更新全選狀態
  updateSelectAllState() {
    if (this.requirementList.length === 0) {
      this.isAllSelected = false;
    } else {
      this.isAllSelected = this.selectedItems.length === this.requirementList.length;
    }
  }

  // 檢查項目是否被選中
  isItemSelected(item: GetRequirement): boolean {
    return this.selectedItems.some(selected => selected.CRequirementID === item.CRequirementID);
  }

  // 開啟批次編輯對話框
  openBatchEdit(dialog: TemplateRef<any>, config?: BatchEditConfig) {
    if (this.selectedItems.length === 0) {
      this.message.showErrorMSG('請先選擇要編輯的項目');
      return;
    }

    // 設定批次編輯配置
    this.batchEditConfig = config || this.getDefaultBatchEditConfig();
    this.isBatchEditMode = true;

    // 初始化批次編輯項目資料
    this.batchEditItems = this.selectedItems.map(item => ({
      CRequirementID: item.CRequirementID,
      CBuildCaseID: item.CBuildCaseID,
      CLocation: item.CLocation,
      CRequirement: item.CRequirement,
      CHouseType: item.CHouseType ? [...item.CHouseType] : [],
      CSort: item.CSort,
      CStatus: item.CStatus,
      CUnitPrice: item.CUnitPrice || 0,
      CUnit: item.CUnit,
      CSpaceId: item.CSpaceId || null,
      CIsShow: item.CIsShow || false,
      CIsSimple: item.CIsSimple || false,
      CRemark: item.CRemark
    }));

    this.dialogService.open(dialog);
  }

  // 批次驗證方法
  batchValidation(): string[] {
    const errorMessages: string[] = [];

    this.batchEditItems.forEach((item, index) => {
      const itemNum = index + 1;

      // 必填欄位檢核
      if (!item.CBuildCaseID) {
        errorMessages.push(`[項目 ${itemNum}] 建案名稱為必填欄位`);
      }

      if (!item.CRequirement || item.CRequirement.trim() === '') {
        errorMessages.push(`[項目 ${itemNum}] 工程項目為必填欄位`);
      }

      if (!item.CHouseType || item.CHouseType.length === 0) {
        errorMessages.push(`[項目 ${itemNum}] 類型為必填欄位`);
      }

      if (item.CSort === null || item.CSort === undefined || item.CSort < 0) {
        errorMessages.push(`[項目 ${itemNum}] 排序為必填欄位且不能為負數`);
      }

      if (item.CStatus === null || item.CStatus === undefined) {
        errorMessages.push(`[項目 ${itemNum}] 狀態為必填欄位`);
      }

      if (item.CUnitPrice === null || item.CUnitPrice === undefined || item.CUnitPrice < 0) {
        errorMessages.push(`[項目 ${itemNum}] 單價為必填欄位且不能為負數`);
      }

      if (!item.CUnit || item.CUnit.trim() === '') {
        errorMessages.push(`[項目 ${itemNum}] 單位為必填欄位`);
      }

      // 長度驗證
      if (item.CLocation && item.CLocation.length > 20) {
        errorMessages.push(`[項目 ${itemNum}] 區域不能超過20個字`);
      }

      if (item.CRequirement && item.CRequirement.length > 50) {
        errorMessages.push(`[項目 ${itemNum}] 工程項目不能超過50個字`);
      }

      if (item.CRemark && item.CRemark.length > 100) {
        errorMessages.push(`[項目 ${itemNum}] 備註說明不能超過100個字`);
      }
    });

    return errorMessages;
  }

  // 批次保存
  batchSave(ref: any) {
    if (this.batchEditItems.length === 0) {
      this.message.showErrorMSG('沒有要更新的項目');
      return;
    }

    // 執行批次驗證
    const validationErrors = this.batchValidation();
    if (validationErrors.length > 0) {
      this.message.showErrorMSGs(validationErrors);
      return;
    }

    // 分離新增項目和更新項目
    const newItems = this.batchEditItems.filter(item => !item.CRequirementID);
    const updateItems = this.batchEditItems.filter(item => item.CRequirementID);

    // 建立請求陣列
    const requests: Promise<any>[] = [];

    // 新增項目的請求
    newItems.forEach(item => {
      const newItemData: SaveDataRequirement = {
        CBuildCaseID: item.CBuildCaseID,
        CLocation: item.CLocation,
        CRequirement: item.CRequirement,
        CHouseType: item.CHouseType,
        CSort: item.CSort,
        CStatus: item.CStatus,
        CUnitPrice: item.CUnitPrice,
        CUnit: item.CUnit,
        CSpaceId: item.CSpaceId,
        CIsShow: item.CIsShow,
        CIsSimple: item.CIsSimple,
        CRemark: item.CRemark
      };

      requests.push(
        this.requirementService.apiRequirementSaveDataPost$Json({
          body: newItemData
        }).toPromise()
      );
    });

    // 更新項目的請求（如果有的話）
    if (updateItems.length > 0) {
      const updateData: SaveDataRequirement[] = updateItems.map(item => ({
        CRequirementID: item.CRequirementID,
        CBuildCaseID: item.CBuildCaseID,
        CLocation: item.CLocation,
        CRequirement: item.CRequirement,
        CHouseType: item.CHouseType,
        CSort: item.CSort,
        CStatus: item.CStatus,
        CUnitPrice: item.CUnitPrice,
        CUnit: item.CUnit,
        CSpaceId: item.CSpaceId,
        CIsShow: item.CIsShow,
        CIsSimple: item.CIsSimple,
        CRemark: item.CRemark
      }));

      requests.push(
        this.requirementService.apiRequirementBatchSaveDataPost$Json({
          body: updateData
        }).toPromise()
      );
    }

    // 執行所有請求
    Promise.all(requests)
      .then(responses => {
        const successCount = responses.filter(res => res?.StatusCode === 0).length;
        const totalItems = this.batchEditItems.length;

        if (successCount === responses.length) {
          this.message.showSucessMSG(`成功處理 ${totalItems} 個項目 (新增: ${newItems.length}, 更新: ${updateItems.length})`);
        } else {
          this.message.showSucessMSG(`成功處理 ${successCount} 個項目，${responses.length - successCount} 個失敗`);
        }

        // 清理狀態並重新載入資料
        this.selectedItems = [];
        this.batchEditItems = [];
        this.isBatchEditMode = false;
        this.updateSelectAllState();
        this.getList();
        ref.close();
      })
      .catch(error => {
        console.error('批次保存失敗:', error);
        this.message.showErrorMSG('批次保存時發生錯誤');
      });
  }

  // 重置批次編輯中的單一項目到原始狀態
  resetBatchEditItem(index: number) {
    const originalItem = this.selectedItems[index];
    if (originalItem) {
      this.batchEditItems[index] = {
        CRequirementID: originalItem.CRequirementID,
        CBuildCaseID: originalItem.CBuildCaseID,
        CLocation: originalItem.CLocation,
        CRequirement: originalItem.CRequirement,
        CHouseType: originalItem.CHouseType ? [...originalItem.CHouseType] : [],
        CSort: originalItem.CSort,
        CStatus: originalItem.CStatus,
        CUnitPrice: originalItem.CUnitPrice || 0,
        CUnit: originalItem.CUnit,
        CSpaceId: originalItem.CSpaceId || null,
        CIsShow: originalItem.CIsShow || false,
        CIsSimple: originalItem.CIsSimple || false,
        CRemark: originalItem.CRemark
      };
    }
  }

  // 取消批次編輯
  cancelBatchEdit(ref: any) {
    this.isBatchEditMode = false;
    this.batchEditItems = [];
    ref.close();
  }

  // 備用：如果需要手動建立需求項目的方法
  private batchCreateRequirements(requirements: SaveDataRequirement[]) {
    const batchRequests = requirements.map(requirement =>
      this.requirementService.apiRequirementSaveDataPost$Json({
        body: requirement
      })
    );

    Promise.all(batchRequests.map(req => req.toPromise()))
      .then(responses => {
        const successCount = responses.filter(res => res?.StatusCode === 0).length;
        this.message.showSucessMSG(`成功建立 ${successCount} 個需求項目`);
        this.getList();
      })
      .catch(error => {
        console.error('批次建立需求失敗:', error);
        this.message.showErrorMSG('批次建立需求時發生錯誤');
      });
  }

  // 導航到模板管理頁面
  navigateToTemplate(): void {
    this.router.navigate(['/pages/template']);
  }

}
